from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime

class CompanyInfo(BaseModel):
    symbol: str
    company_name: str
    about: str
    sector: str

class KeyMetrics(BaseModel):
    market_cap_in_cr: Optional[float] = None
    stock_pe: Optional[float] = None
    current_price: Optional[float] = None
    high: Optional[float] = None
    low: Optional[float] = None
    book_value: Optional[float] = None
    face_value: Optional[float] = None
    dividend_yield_percentage: Optional[float] = None
    roe_percentage: Optional[float] = None
    roce_percentage: Optional[float] = None

class FinancialRecord(BaseModel):
    period: str
    sales_in_cr: Optional[float] = None
    expenses_in_cr: Optional[float] = None
    operating_profit_in_cr: Optional[float] = None
    other_income_in_cr: Optional[float] = None
    interest_in_cr: Optional[float] = None
    depreciation_in_cr: Optional[float] = None
    profit_before_tax_in_cr: Optional[float] = None
    tax_in_cr: Optional[float] = None
    net_profit_in_cr: Optional[float] = None

class BalanceSheetRecord(BaseModel):
    period: str
    equity_capital_in_cr: Optional[float] = None
    reserves_in_cr: Optional[float] = None
    borrowings_in_cr: Optional[float] = None
    other_liabilities_in_cr: Optional[float] = None
    total_liabilities_in_cr: Optional[float] = None
    fixed_assets_in_cr: Optional[float] = None
    capital_work_in_progress_in_cr: Optional[float] = None
    investments_in_cr: Optional[float] = None
    other_assets_in_cr: Optional[float] = None
    total_assets_in_cr: Optional[float] = None

class CashFlowRecord(BaseModel):
    period: str
    cash_from_operating_activity_in_cr: Optional[float] = None
    cash_from_investing_activity_in_cr: Optional[float] = None
    cash_from_financing_activity_in_cr: Optional[float] = None
    net_cash_flow_in_cr: Optional[float] = None

class Financials(BaseModel):
    quarterly_results: List[FinancialRecord] = []
    annual_results: List[FinancialRecord] = []
    balance_sheets: List[BalanceSheetRecord] = []
    cash_flows: List[CashFlowRecord] = []

class ShareholdingRecord(BaseModel):
    period: str
    promoters_percentage: Optional[float] = None
    fiis_percentage: Optional[float] = None
    diis_percentage: Optional[float] = None
    government_percentage: Optional[float] = None
    public_percentage: Optional[float] = None

class ShareholdingPattern(BaseModel):
    quarterly_data: List[ShareholdingRecord] = []
    annual_data: List[ShareholdingRecord] = []

class Concall(BaseModel):
    period: str
    transcript_url: str = ""
    ppt_url: str = ""

class StockDataResponse(BaseModel):
    company_info: CompanyInfo
    key_metrics: KeyMetrics
    financials: Financials
    shareholding_pattern: ShareholdingPattern
    concalls: List[Concall] = []
    scraped_at: datetime = Field(default_factory=datetime.now)