"""
Example API service using the modified screener scraper
This demonstrates how the scraper now works as an API service with JSON caching
"""

from fastapi import FastAPI, HTTPException
from screener_scraper import ScreenerScraper
from models import StockDataResponse
import uvicorn

app = FastAPI(title="Stock Data API", description="API service for stock data scraping with JSON caching")

# Initialize the scraper
scraper = ScreenerScraper(cache_expiry_hours=24)

@app.get("/")
async def root():
    return {"message": "Stock Data API is running! Use /stock/{symbol} to get stock data."}

@app.get("/stock/{symbol}", response_model=StockDataResponse)
async def get_stock_data(symbol: str):
    """
    Get stock data for a given symbol.
    
    - **symbol**: Stock symbol (e.g., ETERNAL, RELIANCE, TCS)
    
    Returns structured stock data with automatic JSON caching.
    If data exists in cache and is not expired, returns cached data.
    Otherwise, scrapes fresh data and caches it.
    """
    try:
        # Convert symbol to uppercase for consistency
        symbol = symbol.upper()
        
        # Get stock data (uses JSON caching automatically)
        stock_data = scraper.scrape_stock_data(symbol)
        
        if stock_data is None:
            raise HTTPException(status_code=404, detail=f"Stock data not found for symbol: {symbol}")
        
        return stock_data
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching stock data: {str(e)}")

@app.get("/stock/{symbol}/company")
async def get_company_info(symbol: str):
    """Get only company information for a stock symbol"""
    stock_data = await get_stock_data(symbol)
    return stock_data.company_info

@app.get("/stock/{symbol}/metrics")
async def get_key_metrics(symbol: str):
    """Get only key financial metrics for a stock symbol"""
    stock_data = await get_stock_data(symbol)
    return stock_data.key_metrics

@app.get("/stock/{symbol}/financials")
async def get_financials(symbol: str):
    """Get only financial data for a stock symbol"""
    stock_data = await get_stock_data(symbol)
    return stock_data.financials

@app.get("/stock/{symbol}/shareholding")
async def get_shareholding_pattern(symbol: str):
    """Get only shareholding pattern for a stock symbol"""
    stock_data = await get_stock_data(symbol)
    return stock_data.shareholding_pattern

@app.get("/stock/{symbol}/concalls")
async def get_concalls(symbol: str):
    """Get only concalls data for a stock symbol"""
    stock_data = await get_stock_data(symbol)
    return stock_data.concalls

if __name__ == "__main__":
    print("🚀 Starting Stock Data API Service...")
    print("📊 Features:")
    print("  - JSON caching (24-hour expiry)")
    print("  - Pydantic model validation")
    print("  - RESTful API endpoints")
    print("  - Automatic data structure validation")
    print("\n🌐 API will be available at: http://localhost:8000")
    print("📖 API docs at: http://localhost:8000/docs")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)
